import type { PatientApiData } from "@/common/apis/patients/type"
import type { PatientInfo, PatientFilterParams } from "../types"

/** 性别映射 - 接口到前端 */
const genderMap: Record<string, "男" | "女" | "未知"> = {
  'Male': '男',
  'Female': '女',
  'UNKNOWN': '未知'
}

/** 状态映射 - 接口到前端 */
const statusMap: Record<string, "待检查" | "检查中" | "分析中" | "已完成" | "已取消"> = {
  'PENDING_CHECK': '待检查',
  'CHECKING': '检查中',
  'ANALYZING': '分析中',
  'COMPLETED': '已完成',
  'CANCELLED': '已取消'
}

/** 状态反向映射 - 前端到接口 */
const statusReverseMap: Record<string, string> = {
  '待检查': 'PENDING_CHECK',
  '检查中': 'CHECKING',
  '分析中': 'ANALYZING',
  '已完成': 'COMPLETED',
  '已取消': 'CANCELLED'
}

/**
 * 格式化日期时间
 * @param isoString ISO格式的时间字符串
 * @returns 格式化后的时间字符串 "YYYY-MM-DD HH:mm"
 */
export function formatDateTime(isoString: string): string {
  if (!isoString) return ''
  
  try {
    const date = new Date(isoString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('时间格式化失败:', error)
    return isoString
  }
}

/**
 * 转换接口患者数据为前端显示数据
 * @param apiData 接口返回的患者数据
 * @returns 前端显示的患者数据
 */
export function transformPatientData(apiData: PatientApiData): PatientInfo {
  return {
    id: String(apiData.id),
    name: apiData.name,
    gender: genderMap[apiData.gender] || '未知',
    age: apiData.age,
    examType: apiData.examType,
    examTime: formatDateTime(apiData.examTime),
    department: apiData.department,
    status: statusMap[apiData.status] || '待检查',
    info: apiData.info,
    createTime: apiData.createTime,
    updateTime: apiData.updateTime
  }
}

/**
 * 转换前端筛选参数为搜索接口参数
 * @param frontendParams 前端筛选参数
 * @returns 搜索接口查询参数
 */
export function transformFilterParams(frontendParams: PatientFilterParams) {
  // 处理状态转换：无论传入中文还是英文状态，都转换为中文状态传递给后端
  let transformedStatus: string | undefined = undefined
  if (frontendParams.status) {
    // 如果传入的是中文状态，直接使用
    if (statusReverseMap[frontendParams.status]) {
      transformedStatus = frontendParams.status
    } else {
      // 如果传入的是英文状态，转换为中文状态
      transformedStatus = statusMap[frontendParams.status] || frontendParams.status
    }
  }

  return {
    pageNumber: frontendParams.currentPage || 1,
    pageSize: frontendParams.size || 10,
    name: frontendParams.name || undefined,
    department: frontendParams.department || undefined,
    status: transformedStatus,
    startTime: frontendParams.startTime || undefined,
    endTime: frontendParams.endTime || undefined
  }
}

/**
 * 转换搜索响应数据为VXE表格期望的格式
 * @param records 患者数据数组
 * @param totalRow 总记录数
 * @returns VXE表格期望的数据格式
 */
export function transformPageResponse(records: PatientApiData[], totalRow: number) {
  return {
    total: totalRow,
    result: records.map(transformPatientData)
  }
}
