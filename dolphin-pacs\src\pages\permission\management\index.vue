<template>
  <div class="p-4">
    <!-- 搜索区域，单独盒子 -->
    <el-card class="mb-4">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="名称">
          <el-input v-model="searchForm.name" placeholder="请输入名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable>
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 + 分页区域 -->
    <el-card>
      <vxe-grid
        ref="xGrid"
        v-bind="xGridOpt"
        :loading="loading"
        :data="tableData"
      >
        <template #status-column="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'info'" size="small">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>

        <template #action-column="{ row }">
          <el-button type="primary" text size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" text size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </vxe-grid>

      <el-pagination
        v-model:current-page="pagination.pageNumber"
        v-model:page-size="pagination.pageSize"
        :total="pagination.totalRow"
        @current-change="fetchData"
        @size-change="fetchData"
        class="mt-4 text-right"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPermissionSearchApi } from '@/common/apis/permission'
import type {
  PermissionItem,
  PermissionSearchParams
} from '@/common/apis/permission/type'

const loading = ref(false)
const tableData = ref<PermissionItem[]>([])

const pagination = reactive({
  pageNumber: 1,
  pageSize: 10,
  totalRow: 0
})

const searchForm = reactive<PermissionSearchParams>({
  name: '',
  status: undefined
})

const xGridOpt = reactive({
  border: true,
  stripe: true,
  autoResize: true,
  columns: [
    { type: 'checkbox' as const, width: 50 },
    { type: 'seq' as const, title: '序号', width: 60 },
    { field: 'id', title: '资源ID', width: 100 },
    { field: 'name', title: '名称', minWidth: 120 },
    { field: 'code', title: '编码', minWidth: 100 },
    { field: 'path', title: '路径', minWidth: 150 },
    { field: 'level', title: '层级', width: 80 },
    { field: 'status', title: '状态', width: 80, slots: { default: 'status-column' } },
    { field: 'createTime', title: '创建时间', minWidth: 160 },
    { field: 'updateTime', title: '更新时间', minWidth: 160 },
    { field: 'remark', title: '备注', minWidth: 120, showOverflow: 'tooltip' as const },
    { title: '操作', width: 120, fixed: 'right' as const, slots: { default: 'action-column' } }
  ]
})

const fetchData = async () => {
  loading.value = true
  try {
    const res = await getPermissionSearchApi({
      ...searchForm,
      pageNumber: pagination.pageNumber,
      pageSize: pagination.pageSize
    })
    tableData.value = res.data.records
    pagination.totalRow = res.data.totalRow
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.name = ''
  searchForm.status = undefined
  pagination.pageNumber = 1
  fetchData()
}

const handleEdit = (row: PermissionItem) => {
  console.log('编辑资源', row)
}

const handleDelete = async (row: PermissionItem) => {
  ElMessageBox.confirm('确定要删除该资源吗？', '提示', { type: 'warning' })
    .then(async () => {
      // TODO: 替换为实际删除接口
      // await deletePermissionApi(row.id)
      ElMessage.success('删除成功')
      fetchData()
    })
}

onMounted(fetchData)
</script>


<style scoped>
.text-right {
  text-align: right;
}
</style>
